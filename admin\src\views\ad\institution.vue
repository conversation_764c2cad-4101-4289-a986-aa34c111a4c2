<template>
  <basic-container>
    <avue-crud :option="option"
               :table-loading="loading"
               :data="data"
               :permission="permissionList"
               :before-open="beforeOpen"
               v-model="form"
               :search="search"
               :page="page"
               ref="crud"
               @row-update="rowUpdate"
               @row-save="rowSave"
               @row-del="rowDel"
               @search-change="searchChange"
               @search-reset="searchReset"
               @selection-change="selectionChange"
               @current-change="currentChange"
               @size-change="sizeChange"
               @on-load="onLoad">
      <template #menu-left>
        <el-button type="danger"
                   size="small"
                   icon="el-icon-delete"
                   plain
                   v-if="permission.institution_delete"
                   @click="handleDelete">删 除
        </el-button>
        <el-button type="success"
                   size="small"
                   icon="el-icon-refresh"
                   plain
                   @click="handleRefresh">刷 新
        </el-button>
        <el-button type="primary"
                   size="small"
                   icon="el-icon-check"
                   plain
                   @click="handleBatchAudit">批量审核
        </el-button>
        <el-button type="warning"
                   size="small"
                   icon="el-icon-switch-button"
                   plain
                   @click="handleBatchOpenOrClose">批量启用/禁用
        </el-button>
      </template>
      <template #auditStatus="{ row }">
        <el-tag :type="getAuditStatusType(row.auditStatus)">
          {{ getAuditStatusText(row.auditStatus) }}
        </el-tag>
      </template>
      <template #status="{ row }">
        <el-tag :type="row.status === 1 ? 'success' : 'danger'">
          {{ row.status === 1 ? '启用' : '禁用' }}
        </el-tag>
      </template>

      <!-- 浏览量插槽 -->
      <template #viewCount="{ row }">
        <el-button
          type="text"
          @click="showDataRecord(row, 'view')"
          :disabled="!getViewCount(row)">
          {{ getViewCount(row) }}
        </el-button>
      </template>

      <!-- 点赞数插槽 -->
      <template #likeCount="{ row }">
        <el-button
          type="text"
          @click="showDataRecord(row, 'like')"
          :disabled="!getLikeCount(row)">
          {{ getLikeCount(row) }}
        </el-button>
      </template>

      <!-- 反馈数插槽 -->
      <template #feedbackCount="{ row }">
        <el-button
          type="text"
          @click="showDataRecord(row, 'feedback')"
          :disabled="!getFeedbackCount(row)">
          {{ getFeedbackCount(row) }}
        </el-button>
      </template>

      <!-- 收藏数插槽 -->
      <template #favoriteCount="{ row }">
        <el-button
          type="text"
          @click="showDataRecord(row, 'favorite')"
          :disabled="!getFavoriteCount(row)">
          {{ getFavoriteCount(row) }}
        </el-button>
      </template>
      <template #description="{ row }">
        <el-button type="text" @click="viewDescription(row)">
          {{ row.description ? (row.description.length > 30 ? row.description.substring(0, 30) + '...' : row.description) : '查看简介' }}
        </el-button>
      </template>
      <template #businessHours="{ row }">
        <el-button type="text" @click="viewBusinessHours(row)">
          查看营业时间
        </el-button>
      </template>
      <template #menu="{ row }">
        <el-button v-if="row.auditStatus === 0" 
                   type="success" 
                   size="small"
                   @click="handleAudit(row, 1)">
          通过
        </el-button>
        <el-button v-if="row.auditStatus === 0" 
                   type="danger" 
                   size="small"
                   @click="handleAudit(row, 2)">
          拒绝
        </el-button>
        <el-button v-if="row.status === 1" 
                   type="warning" 
                   size="small"
                   @click="handleOpenOrClose(row.id, 0)">
          禁用
        </el-button>
        <el-button v-if="row.status === 0" 
                   type="success" 
                   size="small"
                   @click="handleOpenOrClose(row.id, 1)">
          启用
        </el-button>
      </template>
    </avue-crud>

    <!-- 机构详情对话框 -->
    <el-dialog v-model="detailDialogVisible" title="机构详情" width="800px">
      <div v-if="selectedInstitution">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="机构名称">{{ selectedInstitution.name }}</el-descriptions-item>
          <el-descriptions-item label="机构分类">{{ selectedInstitution.typeId }}</el-descriptions-item>
          <el-descriptions-item label="营业年限">{{ selectedInstitution.yearsInBusiness }}年</el-descriptions-item>
          <el-descriptions-item label="联系人">{{ selectedInstitution.contactPerson }}</el-descriptions-item>
          <el-descriptions-item label="联系电话">{{ selectedInstitution.phone }}</el-descriptions-item>
          <el-descriptions-item label="电子邮箱">{{ selectedInstitution.email }}</el-descriptions-item>
          <el-descriptions-item label="法人代表">{{ selectedInstitution.legalPerson }}</el-descriptions-item>
          <el-descriptions-item label="营业执照号">{{ selectedInstitution.licenseNo }}</el-descriptions-item>
          <el-descriptions-item label="地址" :span="2">
            {{ selectedInstitution.province }} {{ selectedInstitution.city }} {{ selectedInstitution.district }} {{ selectedInstitution.detailAddress }}
          </el-descriptions-item>
          <el-descriptions-item label="审核状态">
            <el-tag :type="getAuditStatusType(selectedInstitution.auditStatus)">
              {{ getAuditStatusText(selectedInstitution.auditStatus) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="申请时间">{{ selectedInstitution.applyTime }}</el-descriptions-item>
        </el-descriptions>
        <el-divider />
        <div>
          <h4>机构简介：</h4>
          <p style="white-space: pre-wrap;">{{ selectedInstitution.description }}</p>
        </div>
        <div v-if="selectedInstitution.logo">
          <h4>机构Logo：</h4>
          <el-image 
            :src="selectedInstitution.logo"
            style="width: 100px; height: 100px;"
            :preview-src-list="[selectedInstitution.logo]">
          </el-image>
        </div>
        <div v-if="selectedInstitution.images && getImageList(selectedInstitution.images).length > 0">
          <h4>机构图片：</h4>
          <el-image 
            v-for="(img, index) in getImageList(selectedInstitution.images)" 
            :key="index"
            :src="img"
            style="width: 100px; height: 100px; margin-right: 10px; margin-bottom: 10px;"
            :preview-src-list="getImageList(selectedInstitution.images)">
          </el-image>
        </div>
      </div>
    </el-dialog>

    <!-- 审核对话框 -->
    <el-dialog v-model="auditDialogVisible" title="机构审核" width="500px">
      <el-form :model="auditForm" label-width="100px">
        <el-form-item label="审核结果">
          <el-radio-group v-model="auditForm.auditStatus">
            <el-radio :label="1">通过</el-radio>
            <el-radio :label="2">拒绝</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="审核备注">
          <el-input v-model="auditForm.auditRemark" 
                    type="textarea" 
                    :rows="4" 
                    placeholder="请输入审核备注（可选）">
          </el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="auditDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitAudit">确定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 营业时间对话框 -->
    <el-dialog v-model="businessHoursDialogVisible" title="营业时间" width="600px">
      <div v-if="selectedBusinessHours">
        <el-table :data="businessHoursList" border>
          <el-table-column prop="day" label="星期" width="100"></el-table-column>
          <el-table-column prop="open" label="开始时间" width="120"></el-table-column>
          <el-table-column prop="close" label="结束时间" width="120"></el-table-column>
          <el-table-column prop="status" label="状态">
            <template #default="{ row }">
              <el-tag :type="row.status === 'open' ? 'success' : 'info'">
                {{ row.status === 'open' ? '营业' : '休息' }}
              </el-tag>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-dialog>

    <!-- 数据记录查看对话框 -->
    <DataRecordDialog
      v-if="currentRecordType && dataRecordApiConfig[currentRecordType]"
      v-model="dataRecordDialogVisible"
      :record-type="currentRecordType"
      :relevancy-id="currentRelevancyId"
      :relevancy-type="'2'"
      :api-config="dataRecordApiConfig[currentRecordType]" />
  </basic-container>
</template>

<script>
  import {getPage, getDetail, add, update, remove, audit, openOrClose} from "@/api/ad/institution";
  import {mapGetters} from "vuex";
  import { Picture, Upload, Delete, Refresh } from '@element-plus/icons-vue'
  import { getList as getInstitutionTypeList } from '@/api/ad/institutiontype'
  import { getLazyTree } from "@/api/base/region";
  import DataRecordDialog from '@/components/DataRecordDialog.vue';
  import { dataRecordApiConfig } from '@/api/ad/datarecord';
  import { baseUrl } from '@/config/env';

  export default {
    components: {
      Picture,
      Upload,
      Delete,
      Refresh,
      DataRecordDialog
    },
    data() {
      return {
        form: {},
        query: {},
        search: {},
        loading: true,
        page: {
          pageSize: 10,
          currentPage: 1,
          total: 0
        },
        selectionList: [],
        detailDialogVisible: false,
        selectedInstitution: null,
        auditDialogVisible: false,
        auditForm: {
          institutionIds: null,
          auditStatus: 1,
          auditRemark: ''
        },
        businessHoursDialogVisible: false,
        selectedBusinessHours: null,
        businessHoursList: [],
        // 数据记录对话框相关
        dataRecordDialogVisible: false,
        currentRecordType: '',
        currentRelevancyId: null,
        dataRecordApiConfig: dataRecordApiConfig,
        option: {
          height: 'auto',
          calcHeight: 210,
          searchShow: true,
          searchMenuSpan: 6,
          tip: false,
          border: true,
          index: true,
          viewBtn: true,
          selection: true,
          column: [
            {
              label: "机构名称",
              prop: "name",
              search: true,
              rules: [{
                required: true,
                message: "请输入机构名称",
                trigger: "blur"
              }]
            },
            {
              label: "机构Logo",
              prop: "logo",
              type: 'upload',
              action: '/blade-system/file-upload/upload',
              headers: {
                'Authorization': `Bearer ${localStorage.getItem('blade-auth')}`
              },
              data: {
                uploadSource: 'admin',
                businessType: 'institution-logo'
              },
              accept: 'image/*',
              limit: 1,
              tip: '支持jpg/png/gif格式，建议尺寸200x200px，文件大小不超过2MB',
              propsHttp: {
                url: 'accessUrl',
                name: 'fileName',
                res: 'data'
              },
              rules: [{
                required: false,
                message: "请上传机构Logo",
                trigger: "change"
              }]
            },
            {
              label: "机构分类",
              prop: "typeId",
              type: "select",
              dicData: [], // 动态加载
              props: {
                label: 'name',
                value: 'id'
              },
              search: true,
              rules: [{
                required: true,
                message: "请选择机构分类",
                trigger: "change"
              }]
            },
            {
              label: "营业年限",
              prop: "yearsInBusiness",
              type: "number",
              rules: [{
                required: true,
                message: "请输入营业年限",
                trigger: "blur"
              }]
            },
            {
              label: "机构简介",
              prop: "description",
              type: "textarea",
              span: 24,
              slot: true,
              rules: [{
                required: true,
                message: "请输入机构简介",
                trigger: "blur"
              }]
            },
            {
              label: "联系人",
              prop: "contactPerson",
              search: true,
              rules: [{
                required: true,
                message: "请输入联系人",
                trigger: "blur"
              }]
            },
            {
              label: "联系电话",
              prop: "phone",
              search: true,
              rules: [{
                required: true,
                message: "请输入联系电话",
                trigger: "blur"
              }]
            },
            {
              label: "电子邮箱",
              prop: "email",
              search: true,
              rules: [{
                required: true,
                message: "请输入电子邮箱",
                trigger: "blur"
              }]
            },
            {
              label: "营业执照号",
              prop: "licenseNo",
              search: true,
              rules: [{
                required: true,
                message: "请输入营业执照号",
                trigger: "blur"
              }]
            },
            {
              label: "营业执照照片",
              prop: "licenseImage",
              type: 'upload',
              action: '/blade-system/file-upload/upload',
              headers: {
                'Authorization': `Bearer ${localStorage.getItem('blade-auth')}`
              },
              data: {
                uploadSource: 'admin',
                businessType: 'institution-license'
              },
              accept: 'image/*',
              limit: 1,
              tip: '支持jpg/png/gif格式，文件大小不超过5MB',
              propsHttp: {
                url: 'accessUrl',
                name: 'fileName',
                res: 'data'
              },
              rules: [{
                required: true,
                message: "请上传营业执照照片",
                trigger: "change"
              }]
            },
            {
              label: "法人代表",
              prop: "legalPerson",
              search: true,
              rules: [{
                required: true,
                message: "请输入法人代表",
                trigger: "blur"
              }]
            },
            {
              label: "地区",
              prop: "regionCode",
              type: "cascader",
              dicData: [],
              props: {
                label: "title",
                value: "id",
                children: "children",
              },
              checkStrictly: true,
              showAllLevels: false,
              expandTrigger: 'click',
              emitPath: true,
              clearable: true,
              filterable: true,
              placeholder: "请选择地区",
              rules: [
                {
                  required: true,
                  message: "请选择地区",
                  trigger: "blur"
                }
              ]
            },
            {
              label: "详细地址",
              prop: "detailAddress",
              rules: [{
                required: true,
                message: "请输入详细地址",
                trigger: "blur"
              }]
            },
            {
              label: "服务半径",
              prop: "serviceRadius",
              type: "number",
              rules: [{
                required: true,
                message: "请输入服务半径(米)",
                trigger: "blur"
              }]
            },
            {
              label: "支付方式",
              prop: "paymentMethods",
              rules: [{
                required: true,
                message: "请输入支付方式",
                trigger: "blur"
              }]
            },
            {
              label: "特色服务",
              prop: "specialServices",
              rules: [{
                required: true,
                message: "请输入特色服务",
                trigger: "blur"
              }]
            },
            {
              label: "机构图片",
              prop: "images",
              type: 'upload',
              action: '/blade-system/file-upload/upload',
              headers: {
                'Authorization': `Bearer ${localStorage.getItem('blade-auth')}`
              },
              data: {
                uploadSource: 'admin',
                businessType: 'institution-images'
              },
              accept: 'image/*',
              limit: 9,
              tip: '支持jpg/png/gif格式，最多9张图片，每张文件大小不超过2MB',
              propsHttp: {
                url: 'accessUrl',
                name: 'fileName',
                res: 'data'
              },
              rules: [{
                required: false,
                message: "请上传机构图片",
                trigger: "change"
              }]
            },
            {
              label: "营业时间",
              prop: "businessHours",
              type: "textarea",
              span: 24,
              slot: true,
              rules: [{
                required: true,
                message: "请输入营业时间",
                trigger: "blur"
              }]
            },
            {
              label: "浏览量",
              prop: "viewCount",
              type: "number",
              sortable: true,
              slot: true,
              hide: false
            },
            {
              label: "点赞数",
              prop: "likeCount",
              type: "number",
              sortable: true,
              slot: true,
              hide: false
            },
            {
              label: "反馈数",
              prop: "feedbackCount",
              type: "number",
              sortable: true,
              slot: true,
              hide: false
            },
            {
              label: "收藏数",
              prop: "favoriteCount",
              type: "number",
              sortable: true,
              slot: true,
              hide: false
            },
            {
              label: "审核状态",
              prop: "auditStatus",
              type: "select",
              dicData: [
                { label: '待审核', value: 0 },
                { label: '已通过', value: 1 },
                { label: '已拒绝', value: 2 }
              ],
              search: true,
              slot: true
            },
            {
              label: "审核备注",
              prop: "auditRemark",
              hide: true
            },
            {
              label: "发布状态",
              prop: "publishStatus",
              type: "select",
              dataType: 'number',
              dicUrl: baseUrl + "/blade-system/dict/dictionary?code=common_publish_status",
              props: {
                label: "dictValue",
                value: "dictKey"
              },
              search: true,
            },
            {
              label: "状态",
              prop: "status",
              type: "select",
              dicData: [
                { label: '启用', value: 1 },
                { label: '禁用', value: 0 }
              ],
              search: true,
              slot: true
            },
            {
              label: "申请时间",
              prop: "applyTime",
              type: "datetime",
              format: "YYYY-MM-DD HH:mm:ss",
              valueFormat: "YYYY-MM-DD HH:mm:ss",
              searchSpan: 12
            },
            {
              label: "最后审核时间",
              prop: "lastAuditTime",
              type: "datetime",
              format: "YYYY-MM-DD HH:mm:ss",
              valueFormat: "YYYY-MM-DD HH:mm:ss",
              hide: true
            },
            {
              label: "最后登录时间",
              prop: "lastLoginTime",
              type: "datetime",
              format: "YYYY-MM-DD HH:mm:ss",
              valueFormat: "YYYY-MM-DD HH:mm:ss",
              hide: true
            }
          ]
        },
        data: []
      };
    },
    computed: {
      ...mapGetters(["permission"]),
      permissionList() {
        return {
          addBtn: this.validData(this.permission.institution_add, false),
          viewBtn: this.validData(this.permission.institution_view, false),
          delBtn: this.validData(this.permission.institution_delete, false),
          editBtn: this.validData(this.permission.institution_edit, false)
        };
      },
      ids() {
        let ids = [];
        this.selectionList.forEach(ele => {
          ids.push(ele.id);
        });
        return ids.join(",");
      }
    },
    mounted() {
      // 加载机构分类下拉选项
      getInstitutionTypeList(1, 1000, {}).then(res => {
        const categories = res.data.data.records || [];
        const col = this.option.column.find(c => c.prop === 'typeId');
        if (col) col.dicData = categories;
      });
    },
    methods: {
      // 获取浏览量
      getViewCount(row) {
        // 尝试多种可能的字段名
        if (row.stats && row.stats.viewCount !== undefined) {
          return row.stats.viewCount;
        }
        if (row.viewCount !== undefined) {
          return row.viewCount;
        }
        return 0;
      },
      // 获取点赞数
      getLikeCount(row) {
        // 尝试多种可能的字段名
        if (row.stats && row.stats.likeCount !== undefined) {
          return row.stats.likeCount;
        }
        if (row.likeCount !== undefined) {
          return row.likeCount;
        }
        return 0;
      },
      // 获取反馈数
      getFeedbackCount(row) {
        // 尝试多种可能的字段名
        if (row.stats && row.stats.feedbackCount !== undefined) {
          return row.stats.feedbackCount;
        }
        if (row.feedbackCount !== undefined) {
          return row.feedbackCount;
        }
        return 0;
      },
      // 获取收藏数
      getFavoriteCount(row) {
        // 尝试多种可能的字段名
        if (row.stats && row.stats.favoriteCount !== undefined) {
          return row.stats.favoriteCount;
        }
        if (row.favoriteCount !== undefined) {
          return row.favoriteCount;
        }
        return 0;
      },
      // 显示数据记录对话框
      showDataRecord(row, recordType) {
        if (!row.id) {
          this.$message.warning('机构信息不存在');
          return;
        }

        // 检查对应的数量是否大于0
        let count = 0;
        if (recordType === 'view') {
          count = this.getViewCount(row);
        } else if (recordType === 'like') {
          count = this.getLikeCount(row);
        } else if (recordType === 'feedback') {
          count = this.getFeedbackCount(row);
        } else if (recordType === 'favorite') {
          count = this.getFavoriteCount(row);
        }

        if (count === 0) {
          this.$message.info('暂无相关记录');
          return;
        }

        this.currentRecordType = recordType;
        this.currentRelevancyId = row.id;
        this.dataRecordDialogVisible = true;
      },
      getImageList(images) {
        if (!images) return [];
        if (Array.isArray(images)) return images;
        try {
          const parsed = JSON.parse(images);
          if (Array.isArray(parsed)) {
            return parsed.map(item => item.url || item).filter(url => url);
          }
        } catch (e) {
          return images.split(',').filter(img => img.trim() !== '');
        }
        return [];
      },
      getAuditStatusType(status) {
        const statusMap = {
          0: 'warning',
          1: 'success',
          2: 'danger'
        };
        return statusMap[status] || 'info';
      },
      getAuditStatusText(status) {
        const statusMap = {
          0: '待审核',
          1: '已通过',
          2: '已拒绝'
        };
        return statusMap[status] || '未知';
      },
      viewDescription(row) {
        this.selectedInstitution = row;
        this.detailDialogVisible = true;
      },
      viewBusinessHours(row) {
        this.selectedBusinessHours = row.businessHours;
        try {
          const hours = JSON.parse(row.businessHours);
          this.businessHoursList = Object.entries(hours).map(([day, time]) => ({
            day: this.getDayName(day),
            open: time.open || '--',
            close: time.close || '--',
            status: time.open && time.close ? 'open' : 'closed'
          }));
        } catch (e) {
          this.businessHoursList = [];
        }
        this.businessHoursDialogVisible = true;
      },
      getDayName(day) {
        const dayMap = {
          'monday': '星期一',
          'tuesday': '星期二',
          'wednesday': '星期三',
          'thursday': '星期四',
          'friday': '星期五',
          'saturday': '星期六',
          'sunday': '星期日'
        };
        return dayMap[day] || day;
      },
      handleAudit(row, status) {
        this.auditForm.institutionIds = row.id;
        this.auditForm.auditStatus = status;
        this.auditForm.auditRemark = '';
        this.auditDialogVisible = true;
      },
      handleBatchAudit() {
        if (this.selectionList.length === 0) {
          this.$message.warning("请选择至少一条数据");
          return;
        }
        this.auditForm.institutionIds = this.ids;
        this.auditForm.auditStatus = 1;
        this.auditForm.auditRemark = '';
        this.auditDialogVisible = true;
      },
      handleBatchOpenOrClose() {
        if (this.selectionList.length === 0) {
          this.$message.warning("请选择至少一条数据");
          return;
        }
        this.$confirm("确定要批量启用/禁用选中的机构?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(() => {
          return openOrClose(this.ids);
        }).then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
          this.$refs.crud.toggleSelection();
        });
      },
      handleOpenOrClose(id, status) {
        const action = status === 1 ? '启用' : '禁用';
        this.$confirm(`确定要${action}该机构?`, {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(() => {
          return openOrClose(id);
        }).then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: `${action}成功!`
          });
        });
      },
      submitAudit() {
        audit(this.auditForm).then(() => {
          this.auditDialogVisible = false;
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "审核操作成功!"
          });
        }).catch(error => {
          this.$message.error("审核操作失败");
        });
      },
      rowSave(row, done, loading) {
        add(row).then(() => {
          done();
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        }, error => {
          window.console.log(error);
          loading();
        });
      },
      rowUpdate(row, index, done, loading) {
        update(row).then(() => {
          done();
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        }, error => {
          window.console.log(error);
          loading();
        });
      },
      rowDel(row) {
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(row.id);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
          });
      },
      handleDelete() {
        if (this.selectionList.length === 0) {
          this.$message.warning("请选择至少一条数据");
          return;
        }
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(this.ids);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
            this.$refs.crud.toggleSelection();
          });
      },
      handleRefresh() {
        this.onLoad(this.page);
        this.$message({
          type: "success",
          message: "刷新成功!"
        });
      },
      handleImageError(){
        this.$message.error("图片上传失败");
      },
      beforeOpen(done, type) {
        if (["edit", "view"].includes(type)) {
          getDetail(this.form.id).then(res => {
            this.form = res.data.data;
            // 确保图片字段正确初始化
            if (this.form.logo && typeof this.form.logo === 'string') {
              // Logo字段已经是URL格式，无需处理
            }
            if (this.form.licenseImage && typeof this.form.licenseImage === 'string') {
              // 营业执照图片字段已经是URL格式，无需处理
            }
            if (this.form.images && typeof this.form.images === 'string') {
              // 机构图片字段已经是URL格式，无需处理
            }
          });
        } else {
          // 新增模式，清空图片字段
          this.form.logo = null;
          this.form.licenseImage = null;
          this.form.images = null;
        }
        done();
      },
      searchReset() {
        this.query = {};
        this.onLoad(this.page);
      },
      searchChange(params, done) {
        this.query = params;
        this.page.currentPage = 1;
        this.onLoad(this.page, params);
        done();
      },
      selectionChange(list) {
        this.selectionList = list;
      },
      selectionClear() {
        this.selectionList = [];
        this.$refs.crud.toggleSelection();
      },
      currentChange(currentPage){
        this.page.currentPage = currentPage;
      },
      sizeChange(pageSize){
        this.page.pageSize = pageSize;
      },
      onLoad(page, params = {}) {
        this.loading = true;
        // 加载地区数据
        getLazyTree().then(res => {
          const regionCol = this.option.column.find(c => c.prop === 'regionCode');
          if (regionCol) regionCol.dicData = res.data.data;
        });
        getPage(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
          const data = res.data.data;
          this.page.total = Number(data.total);
          this.data = data.records;
          this.loading = false;
          this.selectionClear();
        });
      }
    }
  };
</script>

<style lang="scss" scoped>
.dialog-footer {
  text-align: right;
}
</style>
